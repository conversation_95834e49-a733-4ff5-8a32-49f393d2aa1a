[2025-07-01 10:27:05,646] INFO - main - main.py:697 - ================================================================================
[2025-07-01 10:27:05,647] INFO - main - main.py:698 - LESSON MANAGER BACKEND STARTING UP
[2025-07-01 10:27:05,647] INFO - main - main.py:699 - ================================================================================
[2025-07-01 10:27:05,647] INFO - main - main.py:700 - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
[2025-07-01 10:27:05,647] INFO - main - main.py:701 - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager
[2025-07-01 10:27:05,647] INFO - main - main.py:702 - Log level: DEBUG
[2025-07-01 10:27:05,647] INFO - main - main.py:703 - ================================================================================
[2025-07-01 10:27:05,647] INFO - main - main.py:705 - Logging configuration complete with immediate console output
[2025-07-01 10:27:05,647] INFO - main - main.py:706 - LOG SETUP COMPLETE - Console output should now be visible
[2025-07-01 10:27:05,649] INFO - main - main.py:781 - INIT_INFO: Flask app instance created and CORS configured.
[2025-07-01 10:27:05,651] INFO - main - main.py:960 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-07-01 10:27:05,651] INFO - main - main.py:989 - Phase transition fixes imported successfully
[2025-07-01 10:27:05,657] INFO - main - main.py:3286 - Successfully imported utils functions
[2025-07-01 10:27:05,657] INFO - main - main.py:3294 - Successfully imported extract_ai_state functions
[2025-07-01 10:27:05,659] INFO - main - main.py:3744 - FLASK: Using unified Firebase initialization approach...
[2025-07-01 10:27:05,659] INFO - unified_firebase_init - unified_firebase_init.py:65 - Firebase already initialized
[2025-07-01 10:27:05,659] INFO - main - main.py:3752 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-07-01 10:27:05,660] INFO - main - main.py:3842 - Gemini API will be initialized on first use (lazy loading).
[2025-07-01 10:27:05,678] INFO - main - main.py:1130 - Successfully imported timetable_generator functions
[2025-07-01 10:27:05,688] WARNING - auth_decorator - auth_decorator.py:56 - Could not fetch student name from Firestore: View function mapping is overwriting an existing endpoint function: static_files
[2025-07-01 10:27:05,690] INFO - auth_decorator - auth_decorator.py:160 - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Development Student
[2025-07-01 10:27:05,690] INFO - auth_decorator - auth_decorator.py:164 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c][require_auth] Development auth: uid=andrea_ugono_33305, name=Development Student
[2025-07-01 10:27:05,691] INFO - __main__ - main.py:5291 - 
================================================================================
[2025-07-01 10:27:05,691] WARNING - __main__ - main.py:5291 - 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
[2025-07-01 10:27:05,692] WARNING - __main__ - main.py:5291 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-07-01 10:27:05,692] INFO - __main__ - main.py:5291 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-001","content_to_enhance":"Yes I am","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","level":"P5","subject":"Artificial Intelligence","session_id":"session_e4dd4bf5-ee5b-450e-9379-49eef27b363f","chat_history":[{"role":"user","content":"To me, \"How AI helps the planet\" means that **smart computers can be like super helpers for Earth**. They can use their clever \"brains\" to solve big problems like pollution, wast...
[2025-07-01 10:27:05,692] INFO - __main__ - main.py:5291 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-AI-001', 'content_to_enhance': 'Yes I am', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Artificial Intelligence', 'session_id': 'session_e4dd4bf5-ee5b-450e-9379-49eef27b363f', 'chat_history': [{'role': 'user', 'content': 'To me, "How AI helps the planet" means that **smart computers can be like super helpers for Earth**. They can use their clever "brains" to solve big problems like pollution, wasting energy, and keeping animals safe. It means AI helps us make better choices to protect our home, the planet, so it stays healthy and beautiful for everyone.', 'timestamp': '2025-07-01T09:25:49.731Z'}, {'role': 'assistant', 'content': '**Question 9 of 10 (Short Answer)**\n\n*Why is it helpful to learn about How AI helps the planet in Artificial Intelligence?*', 'timestamp': '2025-07-01T09:25:52.944Z'}, {'role': 'user', 'content': 'It\'s helpful to learn about "How AI helps the planet" in Artificial Intelligence because it shows us:  * **Real-world impact:** It\'s not just a complicated computer thing; it actually solves big, important problems for our Earth. * **What AI can do:** It teaches us the amazing power of AI to analyze huge amounts of data and make smart decisions to protect nature. * **Hope for the future:** It gives us hope that we can use new technology to make our world a better and healthier place for everyone, including animals.', 'timestamp': '2025-07-01T09:26:15.562Z'}, {'role': 'assistant', 'content': '**Question 10 of 10 (Short Answer)**\n\n*Give an example of how How AI helps the planet might be used in real life.*', 'timestamp': '2025-07-01T09:26:19.724Z'}, {'role': 'user', 'content': "One real-life example of how AI helps the planet is **smart thermostats in homes**.  These thermostats use AI to learn your family's schedule and preferences. They know when you're home, when you're asleep, and even what the weather is like outside. Because of this, they can automatically adjust the heating or cooling so you're only using energy when you need it. This stops you from wasting electricity by heating or cooling an empty house, which saves energy and helps reduce pollution.", 'timestamp': '2025-07-01T09:26:38.176Z'}, {'role': 'assistant', 'content': "🎉 Excellent work, Andrea! You've completed the quiz. Let me compile your results.", 'timestamp': '2025-07-01T09:26:42.277Z'}, {'role': 'user', 'content': 'Ok', 'timestamp': '2025-07-01T09:26:47.104Z'}, {'role': 'assistant', 'content': "🎯 **Quiz Results for How AI helps the planet**\n\nGood Work! 😊 Student! Here's your detailed performance analysis:\n- Score: 70% (7/10 correct)\n- You've successfully completed the quiz portion of the lesson!\n\nReady to wrap up with a summary of everything you've learned?", 'timestamp': '2025-07-01T09:26:51.174Z'}]}
[2025-07-01 10:27:05,693] INFO - __main__ - main.py:5291 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍  - Session ID from payload: session_e4dd4bf5-ee5b-450e-9379-49eef27b363f
[2025-07-01 10:27:05,693] INFO - __main__ - main.py:5291 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍  - Student ID from payload: andrea_ugono_33305
[2025-07-01 10:27:05,693] INFO - __main__ - main.py:5291 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍  - Lesson Ref from payload: P5-AI-001
[2025-07-01 10:27:05,693] DEBUG - __main__ - main.py:5348 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='session_e4dd4bf5-ee5b-450e-9379-49eef27b363f', lesson_ref='P5-AI-001'
[2025-07-01 10:27:05,693] INFO - __main__ - main.py:5349 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] Parsed Params: student_id='andrea_ugono_33305', session_id='session_e4dd4bf5-ee5b-450e-9379-49eef27b363f', lesson_ref='P5-AI-001'
[2025-07-01 10:27:05,966] INFO - __main__ - main.py:4639 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-07-01 10:27:05,966] DEBUG - __main__ - main.py:739 - Cache hit for fetch_lesson_data
[2025-07-01 10:27:05,966] INFO - __main__ - main.py:5399 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅ All required fields present after lesson content parsing and mapping
[2025-07-01 10:27:05,967] INFO - __main__ - main.py:5438 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
[2025-07-01 10:27:05,967] INFO - __main__ - main.py:2420 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'AI: Saving Our Planet!'.
[2025-07-01 10:27:06,382] INFO - __main__ - main.py:2479 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
[2025-07-01 10:27:06,382] INFO - __main__ - main.py:2479 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
[2025-07-01 10:27:06,382] INFO - __main__ - main.py:2479 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
[2025-07-01 10:27:06,382] INFO - __main__ - main.py:2479 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
[2025-07-01 10:27:06,383] INFO - __main__ - main.py:2479 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
[2025-07-01 10:27:06,383] INFO - __main__ - main.py:2548 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
[2025-07-01 10:27:06,383] DEBUG - __main__ - main.py:2562 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
[2025-07-01 10:27:06,383] DEBUG - __main__ - main.py:2565 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introducti...
[2025-07-01 10:27:06,384] DEBUG - __main__ - main.py:2566 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference Lesson Summary (first 300 chars): Lesson Title: AI: Saving Our Planet!. Topic: How AI helps the planet. Learning Objectives: Explore AI’s role in saving energy.; Understand AI’s impact on wildlife conservation.. Key Concepts: Explore; role; saving; energy; Understand; impact; wildlife; conservation; What; Energy Saving. Introduction...
[2025-07-01 10:27:06,384] DEBUG - __main__ - main.py:2567 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
[2025-07-01 10:27:06,384] INFO - __main__ - main.py:2571 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference: Calling Gemini API for module inference...
[2025-07-01 10:27:06,902] INFO - __main__ - main.py:2581 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference: Gemini API call completed in 0.52s. Raw response: 'ai_tools_and_applications'
[2025-07-01 10:27:06,903] DEBUG - __main__ - main.py:2603 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference: Cleaned slug: 'ai_tools_and_applications'
[2025-07-01 10:27:06,903] INFO - __main__ - main.py:2608 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
[2025-07-01 10:27:06,903] INFO - __main__ - main.py:5472 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] Successfully inferred module ID via AI: ai_tools_and_applications
[2025-07-01 10:27:06,903] INFO - __main__ - main.py:5509 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
[2025-07-01 10:27:07,181] INFO - __main__ - main.py:2138 - No prior student performance document found for Topic: artificial_intelligence_Primary 5_artificial_intelligence_ai_tools_and_applications
[2025-07-01 10:27:07,875] DEBUG - __main__ - main.py:5525 - 🔍 SESSION STATE RETRIEVAL:
[2025-07-01 10:27:07,875] DEBUG - __main__ - main.py:5526 - 🔍   - Session ID: session_e4dd4bf5-ee5b-450e-9379-49eef27b363f
[2025-07-01 10:27:07,875] DEBUG - __main__ - main.py:5527 - 🔍   - Document Exists: True
[2025-07-01 10:27:07,875] DEBUG - __main__ - main.py:5528 - 🔍   - Current Phase: conclusion_summary
[2025-07-01 10:27:07,875] DEBUG - __main__ - main.py:5529 - 🔍   - Probing Level: 5
[2025-07-01 10:27:07,876] DEBUG - __main__ - main.py:5530 - 🔍   - Question Index: 0
[2025-07-01 10:27:07,876] WARNING - __main__ - main.py:5536 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍 SESSION STATE DEBUG:
[2025-07-01 10:27:07,876] WARNING - __main__ - main.py:5537 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍   - Session exists: True
[2025-07-01 10:27:07,876] WARNING - __main__ - main.py:5538 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍   - Current phase: conclusion_summary
[2025-07-01 10:27:07,876] WARNING - __main__ - main.py:5539 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'student_name', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'is_first_encounter_for_module', 'current_phase', 'current_lesson_phase', 'current_quiz_question', 'teaching_interactions', 'last_modified', 'latest_assessed_level_for_module', 'quiz_answers', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'teaching_complete', 'assigned_level_for_teaching', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-07-01 10:27:07,876] DEBUG - __main__ - main.py:5558 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
[2025-07-01 10:27:07,876] DEBUG - __main__ - main.py:5559 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] � Retrieved Phase: 'conclusion_summary'
[2025-07-01 10:27:07,876] DEBUG - __main__ - main.py:5560 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] � Diagnostic Completed: False
[2025-07-01 10:27:07,877] DEBUG - __main__ - main.py:5561 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] � Assigned Level: None
[2025-07-01 10:27:07,877] WARNING - __main__ - main.py:5562 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔒 STATE PROTECTION: phase='conclusion_summary', diagnostic_done=False, level=None
[2025-07-01 10:27:07,877] INFO - __main__ - main.py:5606 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅ State protection not triggered (diagnostic=False, level=None)
[2025-07-01 10:27:07,877] INFO - __main__ - main.py:5607 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] State protection not triggered
[2025-07-01 10:27:07,877] INFO - __main__ - main.py:5648 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] �🔍 FIRST ENCOUNTER LOGIC:
[2025-07-01 10:27:07,877] INFO - __main__ - main.py:5649 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   assigned_level_for_teaching (session): None
[2025-07-01 10:27:07,877] INFO - __main__ - main.py:5650 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   latest_assessed_level (profile): None
[2025-07-01 10:27:07,878] INFO - __main__ - main.py:5651 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   teaching_level_for_returning_student: None
[2025-07-01 10:27:07,878] INFO - __main__ - main.py:5652 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   has_completed_diagnostic_before: False
[2025-07-01 10:27:07,878] INFO - __main__ - main.py:5653 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   is_first_encounter_for_module: True
[2025-07-01 10:27:07,878] WARNING - __main__ - main.py:5658 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-07-01 10:27:07,878] INFO - __main__ - main.py:5664 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍 PHASE INVESTIGATION:
[2025-07-01 10:27:07,878] INFO - __main__ - main.py:5665 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   Retrieved from Firestore: 'conclusion_summary'
[2025-07-01 10:27:07,878] INFO - __main__ - main.py:5666 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-07-01 10:27:07,878] INFO - __main__ - main.py:5667 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   Is first encounter: True
[2025-07-01 10:27:07,879] INFO - __main__ - main.py:5668 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   Diagnostic completed: False
[2025-07-01 10:27:07,879] INFO - __main__ - main.py:5674 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅ Using stored phase from Firestore: 'conclusion_summary'
[2025-07-01 10:27:07,879] INFO - __main__ - main.py:5688 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-07-01 10:27:07,879] INFO - __main__ - main.py:5690 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] Final phase for AI logic: conclusion_summary
[2025-07-01 10:27:07,879] INFO - __main__ - main.py:5710 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-07-01 10:27:07,879] INFO - __main__ - main.py:3907 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] Diagnostic context validation passed
[2025-07-01 10:27:07,879] INFO - __main__ - main.py:3928 - DETERMINE_PHASE: Preserving advanced phase: 'conclusion_summary' - no backward transitions allowed
[2025-07-01 10:27:07,880] WARNING - __main__ - main.py:5798 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'conclusion_summary' for first encounter
[2025-07-01 10:27:07,880] INFO - __main__ - main.py:5819 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] Skipping diagnostic context enhancement for non-diagnostic phase: conclusion_summary
[2025-07-01 10:27:07,880] DEBUG - __main__ - main.py:5826 - 🧪 DEBUG PHASE: current_phase_for_ai = 'conclusion_summary'
[2025-07-01 10:27:07,880] DEBUG - __main__ - main.py:5827 - 🧪 DEBUG PHASE: determined_phase = 'conclusion_summary'
[2025-07-01 10:27:07,880] INFO - __main__ - main.py:5833 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] Robust context prepared successfully. Phase: conclusion_summary
[2025-07-01 10:27:07,880] DEBUG - __main__ - main.py:5834 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai']
[2025-07-01 10:27:07,880] WARNING - __main__ - main.py:6004 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🤖 AI PROMPT GENERATION:
[2025-07-01 10:27:07,881] WARNING - __main__ - main.py:6005 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🤖   - Current phase: conclusion_summary
[2025-07-01 10:27:07,881] WARNING - __main__ - main.py:6006 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🤖   - Student query: Yes I am...
[2025-07-01 10:27:07,881] WARNING - __main__ - main.py:6007 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai']
[2025-07-01 10:27:07,881] DEBUG - __main__ - main.py:6010 - 🤖 GENERATING AI PROMPT:
[2025-07-01 10:27:07,881] DEBUG - __main__ - main.py:6011 - 🤖   Phase: conclusion_summary
[2025-07-01 10:27:07,882] DEBUG - __main__ - main.py:6012 - 🤖   Query: Yes I am...
[2025-07-01 10:27:07,882] DEBUG - __main__ - main.py:6013 - 🤖   Student: Andrea
[2025-07-01 10:27:07,882] INFO - __main__ - main.py:6939 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] enhance_lesson_content invoked. Query: 'Yes I am...'
[2025-07-01 10:27:07,882] INFO - __main__ - main.py:7030 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🎯 CONCLUSION SUMMARY PHASE: Using enhanced handler...
[2025-07-01 10:27:07,882] INFO - __main__ - main.py:15010 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c][Conclusion] 🚀 Handling conclusion: Generating student summary AND finalizing session data.
[2025-07-01 10:27:07,883] ERROR - __main__ - main.py:15099 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c][Conclusion] Critical error in conclusion/finalization phase: name 'get_lesson_state_from_firestore' is not defined
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\main.py", line 15030, in handle_conclusion_summary_phase
    state_data = get_lesson_state_from_firestore(session_id)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_lesson_state_from_firestore' is not defined
[2025-07-01 10:27:07,932] INFO - __main__ - main.py:7039 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🎯 Enhanced conclusion summary handler completed -> completed
[2025-07-01 10:27:07,933] WARNING - __main__ - main.py:6035 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🤖 AI RESPONSE RECEIVED:
[2025-07-01 10:27:07,933] WARNING - __main__ - main.py:6036 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🤖   - Content length: 53 chars
[2025-07-01 10:27:07,933] WARNING - __main__ - main.py:6037 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🤖   - State updates: {'new_phase': 'completed', 'lesson_complete': True, 'error': "name 'get_lesson_state_from_firestore' is not defined"}
[2025-07-01 10:27:07,933] WARNING - __main__ - main.py:6038 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🤖   - Raw state block: None...
[2025-07-01 10:27:07,933] DEBUG - __main__ - main.py:6041 - 🤖 AI RESPONSE PROCESSED:
[2025-07-01 10:27:07,933] DEBUG - __main__ - main.py:6042 - 🤖   Content: Great job! You've completed this lesson successfully!...
[2025-07-01 10:27:07,934] DEBUG - __main__ - main.py:6043 - 🤖   State: {'new_phase': 'completed', 'lesson_complete': True, 'error': "name 'get_lesson_state_from_firestore' is not defined"}
[2025-07-01 10:27:07,934] INFO - __main__ - main.py:6069 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-07-01 10:27:07,934] INFO - __main__ - main.py:6070 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] CURRENT PHASE DETERMINATION: AI=completed, Session=conclusion_summary, Final=completed
[2025-07-01 10:27:08,206] INFO - __main__ - main.py:6119 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI processing completed in 0.33s
[2025-07-01 10:27:08,206] WARNING - __main__ - main.py:6130 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍 STATE UPDATE VALIDATION: current_phase='conclusion_summary', new_phase='completed'
[2025-07-01 10:27:08,207] INFO - __main__ - main.py:4112 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI state update validation passed: conclusion_summary → completed
[2025-07-01 10:27:08,207] WARNING - __main__ - main.py:6139 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅ STATE UPDATE VALIDATION PASSED
[2025-07-01 10:27:08,207] WARNING - __main__ - main.py:6144 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔄 PHASE TRANSITION: conclusion_summary → completed
[2025-07-01 10:27:08,208] WARNING - __main__ - main.py:6153 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-07-01 10:27:08,208] WARNING - __main__ - main.py:6154 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍   1. Input phase: 'conclusion_summary'
[2025-07-01 10:27:08,208] WARNING - __main__ - main.py:6155 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-07-01 10:27:08,208] WARNING - __main__ - main.py:6156 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍   3. AI state updates: {'new_phase': 'completed', 'lesson_complete': True, 'error': "name 'get_lesson_state_from_firestore' is not defined"}
[2025-07-01 10:27:08,209] WARNING - __main__ - main.py:6157 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍   4. Final phase to save: 'completed'
[2025-07-01 10:27:08,209] WARNING - __main__ - main.py:6160 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 💾 FINAL STATE APPLICATION:
[2025-07-01 10:27:08,209] WARNING - __main__ - main.py:6161 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 💾   - Current phase input: 'conclusion_summary'
[2025-07-01 10:27:08,210] WARNING - __main__ - main.py:6162 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 💾   - State updates from AI: {'new_phase': 'completed', 'lesson_complete': True, 'error': "name 'get_lesson_state_from_firestore' is not defined"}
[2025-07-01 10:27:08,210] WARNING - __main__ - main.py:6163 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 💾   - Final phase to save: 'completed'
[2025-07-01 10:27:08,210] WARNING - __main__ - main.py:6164 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 💾   - Phase change: True
[2025-07-01 10:27:08,210] INFO - __main__ - main.py:4144 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] DIAGNOSTIC_FLOW_METRICS:
[2025-07-01 10:27:08,211] INFO - __main__ - main.py:4145 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   Phase transition: conclusion_summary -> completed
[2025-07-01 10:27:08,211] INFO - __main__ - main.py:4146 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   Current level: 5
[2025-07-01 10:27:08,211] INFO - __main__ - main.py:4147 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   Question index: 0
[2025-07-01 10:27:08,211] INFO - __main__ - main.py:4148 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   First encounter: True
[2025-07-01 10:27:08,211] INFO - __main__ - main.py:4153 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   Answers collected: 5
[2025-07-01 10:27:08,212] INFO - __main__ - main.py:4154 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   Levels failed: 0
[2025-07-01 10:27:08,212] INFO - __main__ - main.py:4112 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI state update validation passed: conclusion_summary → completed
[2025-07-01 10:27:08,212] INFO - __main__ - main.py:4158 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   State update valid: True
[2025-07-01 10:27:08,212] INFO - __main__ - main.py:4165 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c]   Diagnostic complete: False
[2025-07-01 10:27:08,213] WARNING - __main__ - main.py:6177 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
[2025-07-01 10:27:08,213] INFO - __main__ - main.py:6186 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-07-01 10:27:08,213] INFO - __main__ - main.py:6187 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] 🔍 DEBUG original teaching_interactions: 12
[2025-07-01 10:27:08,794] WARNING - __main__ - main.py:6232 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-07-01 10:27:08,794] WARNING - __main__ - main.py:6233 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅   - Phase: completed
[2025-07-01 10:27:08,794] WARNING - __main__ - main.py:6234 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅   - Probing Level: 5
[2025-07-01 10:27:08,794] WARNING - __main__ - main.py:6235 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅   - Question Index: 0
[2025-07-01 10:27:08,794] WARNING - __main__ - main.py:6236 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅   - Diagnostic Complete: False
[2025-07-01 10:27:08,794] WARNING - __main__ - main.py:6243 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅   - Quiz Questions Saved: 10
[2025-07-01 10:27:08,794] WARNING - __main__ - main.py:6244 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅   - Quiz Answers Saved: 10
[2025-07-01 10:27:08,794] WARNING - __main__ - main.py:6245 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅   - Quiz Started: True
[2025-07-01 10:27:08,794] DEBUG - __main__ - main.py:6248 - 🔥 STATE SAVED - Session: session_e4dd4bf5-ee5b-450e-9379-49eef27b363f, Phase: completed
[2025-07-01 10:27:08,795] DEBUG - __main__ - main.py:6249 - 🔥 QUIZ DATA - Questions: 10, Answers: 10
[2025-07-01 10:27:09,645] DEBUG - __main__ - main.py:6307 - ✅ SESSION UPDATED - ID: session_e4dd4bf5-ee5b-450e-9379-49eef27b363f, Phase: completed
[2025-07-01 10:27:09,645] DEBUG - __main__ - main.py:6308 - ✅ INTERACTION LOGGED - Phase: conclusion_summary → completed
[2025-07-01 10:27:09,646] INFO - __main__ - main.py:6314 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅ Updated existing session document: session_e4dd4bf5-ee5b-450e-9379-49eef27b363f
[2025-07-01 10:27:09,646] WARNING - __main__ - main.py:6315 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅ SESSION UPDATE COMPLETE:
[2025-07-01 10:27:09,646] WARNING - __main__ - main.py:6316 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅   - Session ID: session_e4dd4bf5-ee5b-450e-9379-49eef27b363f
[2025-07-01 10:27:09,646] WARNING - __main__ - main.py:6317 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅   - Phase transition: conclusion_summary → completed
[2025-07-01 10:27:09,646] WARNING - __main__ - main.py:6318 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] ✅   - Interaction logged successfully
[2025-07-01 10:27:09,646] INFO - __main__ - main.py:13173 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-07-01 10:27:09,646] DEBUG - __main__ - main.py:2900 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-07-01 10:27:09,646] DEBUG - __main__ - main.py:6373 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] No final assessment data found in AI response
[2025-07-01 10:27:09,646] DEBUG - __main__ - main.py:6401 - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
[2025-07-01 10:27:09,646] DEBUG - __main__ - main.py:6402 - 🔒   Current Phase: conclusion_summary
[2025-07-01 10:27:09,647] DEBUG - __main__ - main.py:6403 - 🔒   Final Phase: completed
[2025-07-01 10:27:09,647] DEBUG - __main__ - main.py:6404 - 🔒   Diagnostic Complete: False
[2025-07-01 10:27:09,647] DEBUG - __main__ - main.py:6405 - 🔒   Assigned Level: None
[2025-07-01 10:27:09,647] DEBUG - __main__ - main.py:6462 - 🎯 RESPONSE READY:
[2025-07-01 10:27:09,647] DEBUG - __main__ - main.py:6463 - 🎯   Session: session_e4dd4bf5-ee5b-450e-9379-49eef27b363f
[2025-07-01 10:27:09,647] DEBUG - __main__ - main.py:6464 - 🎯   Phase: conclusion_summary → completed
[2025-07-01 10:27:09,647] DEBUG - __main__ - main.py:6465 - 🎯   Content: Great job! You've completed this lesson successful...
[2025-07-01 10:27:09,647] DEBUG - __main__ - main.py:6466 - 🎯   Request ID: c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c
[2025-07-01 10:27:09,647] INFO - __main__ - main.py:6472 - [c8f19728-cbe3-45ac-8fc3-1bc3ab618e8c] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-07-01 10:27:09,648] WARNING - __main__ - main.py:768 - High response time detected: 3.96s for enhance_content_api
